package com.admin.task;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 测试WebSocket数据格式转换
 */
public class DataFormatTest {
    
    public static void main(String[] args) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 模拟WebSocket接收到的数据
        String webSocketData = """
        {
            "stream": "btcusdt@ticker",
            "data": {
                "e": "24hrTicker",
                "s": "BTCUSDT",
                "p": "1465.12000000",
                "P": "1.243",
                "w": "118802.75972344",
                "c": "119312.94000000",
                "Q": "0.02226000",
                "o": "117847.82000000",
                "h": "119766.65000000",
                "l": "117840.00000000",
                "v": "9589.74902000",
                "q": "1139288648.63115010",
                "O": 1753576956012,
                "C": 1753663356012,
                "F": "5114264421",
                "L": "5115401124",
                "n": "1136704"
            }
        }
        """;
        
        // 解析数据
        var root = objectMapper.readTree(webSocketData);
        var data = root.get("data");
        
        // 转换为BinanceTickerResponse
        var ticker = objectMapper.treeToValue(data, BinanceWebSocketPriceSyncTask.BinanceTickerResponse.class);
        
        // 设置缺失的字段
        ticker.prevClosePrice = ticker.openPrice;
        ticker.bidPrice = ticker.lastPrice;
        ticker.bidQty = "0.00000000";
        ticker.askPrice = ticker.lastPrice;
        ticker.askQty = "0.00000000";
        
        // 输出最终的JSON格式
        String finalJson = objectMapper.writeValueAsString(ticker);
        System.out.println("WebSocket转换后的数据格式:");
        System.out.println(finalJson);
        
        System.out.println("\n对比REST API格式:");
        System.out.println("REST API应该包含的字段:");
        System.out.println("symbol, priceChange, priceChangePercent, weightedAvgPrice,");
        System.out.println("prevClosePrice, lastPrice, lastQty, bidPrice, bidQty,");
        System.out.println("askPrice, askQty, openPrice, highPrice, lowPrice,");
        System.out.println("volume, quoteVolume, openTime, closeTime, firstId, lastId, count");
    }
}
