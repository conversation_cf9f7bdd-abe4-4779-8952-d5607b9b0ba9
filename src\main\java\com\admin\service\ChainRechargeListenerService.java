package com.admin.service;

import com.admin.entity.UserWalletAddress;
import com.admin.entity.FrontUser;
import com.admin.entity.RechargeRecord;
import com.admin.mapper.UserWalletAddressMapper;
import com.admin.mapper.FrontUserMapper;
import com.admin.mapper.TradeRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.utils.Numeric;
import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ChainRechargeListenerService {

    @Autowired
    private UserWalletAddressMapper userWalletAddressMapper;
    @Autowired
    private FrontUserMapper frontUserMapper;
    @Autowired
    private RechargeRecordService rechargeRecordService;
    @Autowired
    private TradeRecordMapper tradeRecordMapper;

    @PostConstruct
    public void startUsdtListener() {
        String usdtContract = "******************************************"; // BSC主网USDT
        Web3j web3j = Web3j.build(new HttpService("https://bsc-dataseed4.bnbchain.org/"));

        EthFilter filter = new EthFilter(
                DefaultBlockParameterName.LATEST,
                DefaultBlockParameterName.LATEST,
                usdtContract
        );
        String transferEventTopic = EventEncoder.encode(new Event("Transfer",
                Arrays.asList(
                        TypeReference.create(Address.class, true),
                        TypeReference.create(Address.class, true),
                        TypeReference.create(Uint256.class)
                )
        ));
        filter.addSingleTopic(transferEventTopic);

        web3j.ethLogFlowable(filter).subscribe(log -> {
            String to = "0x" + log.getTopics().get(2).substring(26);
            BigInteger value = Numeric.toBigInt(log.getData());
            String txHash = log.getTransactionHash();

            // 检查to地址是否在user_wallet_address表
            UserWalletAddress wallet = userWalletAddressMapper.findByAddress(to);
            if (wallet != null) {
                Long userId = wallet.getUserId();
                FrontUser user = frontUserMapper.selectById(userId);
                BigDecimal amount = new BigDecimal(value).movePointLeft(18); // USDT精度

                // 在事务中处理充值逻辑
                processRechargeInTransaction(userId, user, amount, txHash);
            }
        });
    }

    /**
     * 在事务中处理充值逻辑，确保数据一致性
     */
    @Transactional
    public void processRechargeInTransaction(Long userId, FrontUser user, BigDecimal amount, String txHash) {
        try {
            // 写入充值明细
            RechargeRecord record = new RechargeRecord();
            record.setUserId(userId);
            record.setUsername(user.getUsername());
            record.setEmail(user.getEmail());
            record.setAmount(amount);
            record.setRechargeType(1); // 链上充值
            record.setAuditStatus(1); // 自动通过
            record.setRemark("链上充值自动入账，txHash:" + txHash);
            record.setTxHash(txHash);
            record.setCreateTime(LocalDateTime.now());
            rechargeRecordService.save(record);

            // 写入交易明细表
            tradeRecordMapper.insertTradeRecord(userId, user.getUsername(), "链上充值", amount, 1, "链上充值自动入账，txHash:" + txHash);

            // 累加资金账户充值币
            frontUserMapper.updateAvailableBalanceCZ(userId, amount);

            // 检查用户总充值金额，如果达到1000则激活账户
            checkAndActivateUser(userId);

            log.info("用户ID: {} 链上充值处理完成，金额: {} USDT，txHash: {}", userId, amount, txHash);
        } catch (Exception e) {
            log.error("处理链上充值失败，用户ID: {}, 金额: {}, txHash: {}", userId, amount, txHash, e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 检查用户总充值金额，如果达到1000则激活账户
     */
    private void checkAndActivateUser(Long userId) {
        try {
            // 1. 检查用户是否已激活
            Integer isActivated = frontUserMapper.checkUserActivationStatus(userId);
            if (isActivated != null && isActivated == 1) {
                log.debug("用户ID: {} 已经激活，无需重复激活", userId);
                return;
            }

            // 2. 查询用户总充值金额
            BigDecimal totalRecharge = frontUserMapper.getTotalRechargeAmount(userId);
            log.info("用户ID: {} 总充值金额: {}", userId, totalRecharge);

            // 3. 如果总充值金额大于等于1000，则激活用户
            if (totalRecharge.compareTo(new BigDecimal("1000")) >= 0) {
                int updateCount = frontUserMapper.activateUserAccount(userId);
                if (updateCount > 0) {
                    log.info("用户ID: {} 充值金额达到 {} USDT，账户已自动激活", userId, totalRecharge);
                } else {
                    log.warn("用户ID: {} 激活失败，可能已经激活或用户不存在", userId);
                }
            } else {
                log.debug("用户ID: {} 充值金额 {} USDT 未达到激活条件(1000 USDT)", userId, totalRecharge);
            }
        } catch (Exception e) {
            log.error("检查并激活用户失败，用户ID: {}", userId, e);
        }
    }
}