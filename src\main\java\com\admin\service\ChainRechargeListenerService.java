package com.admin.service;

import com.admin.entity.UserWalletAddress;
import com.admin.entity.FrontUser;
import com.admin.entity.RechargeRecord;
import com.admin.mapper.UserWalletAddressMapper;
import com.admin.mapper.FrontUserMapper;
import com.admin.mapper.TradeRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.utils.Numeric;
import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Arrays;

@Service
public class ChainRechargeListenerService {

    @Autowired
    private UserWalletAddressMapper userWalletAddressMapper;
    @Autowired
    private FrontUserMapper frontUserMapper;
    @Autowired
    private RechargeRecordService rechargeRecordService;
    @Autowired
    private TradeRecordMapper tradeRecordMapper;

    @PostConstruct
    public void startUsdtListener() {
        String usdtContract = "******************************************"; // BSC主网USDT
        Web3j web3j = Web3j.build(new HttpService("https://bsc-dataseed4.bnbchain.org/"));

        EthFilter filter = new EthFilter(
                DefaultBlockParameterName.LATEST,
                DefaultBlockParameterName.LATEST,
                usdtContract
        );
        String transferEventTopic = EventEncoder.encode(new Event("Transfer",
                Arrays.asList(
                        TypeReference.create(Address.class, true),
                        TypeReference.create(Address.class, true),
                        TypeReference.create(Uint256.class)
                )
        ));
        filter.addSingleTopic(transferEventTopic);

        web3j.ethLogFlowable(filter).subscribe(log -> {
            String to = "0x" + log.getTopics().get(2).substring(26);
            BigInteger value = Numeric.toBigInt(log.getData());
            String txHash = log.getTransactionHash();

            // 检查to地址是否在user_wallet_address表
            UserWalletAddress wallet = userWalletAddressMapper.findByAddress(to);
            if (wallet != null) {
                Long userId = wallet.getUserId();
                FrontUser user = frontUserMapper.selectById(userId);
                BigDecimal amount = new BigDecimal(value).movePointLeft(18); // USDT精度

                // 写入充值明细
                RechargeRecord record = new RechargeRecord();
                record.setUserId(userId);
                record.setUsername(user.getUsername());
                record.setEmail(user.getEmail());
                record.setAmount(amount);
                record.setRechargeType(1); // 链上充值
                record.setAuditStatus(1); // 自动通过
                record.setRemark("链上充值自动入账，txHash:" + txHash);
                record.setTxHash(txHash);
                record.setCreateTime(LocalDateTime.now());
                rechargeRecordService.save(record);
                
                // 写入交易明细表
                tradeRecordMapper.insertTradeRecord(userId, user.getUsername(), "链上充值", amount, 1, "链上充值自动入账，txHash:" + txHash);
            
                // 累加资金账户充值币
                frontUserMapper.updateAvailableBalanceCZ(userId, amount);
            }
        });
    }
} 