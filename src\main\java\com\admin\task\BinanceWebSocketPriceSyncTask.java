package com.admin.task;

import com.admin.mapper.ExchangePairMapper;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.URI;
import java.util.List;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BinanceWebSocketPriceSyncTask {

    @Autowired
    private ExchangePairMapper exchangePairInfoMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    private WebSocketClient webSocketClient;
    private final ConcurrentHashMap<String, BinanceTickerResponse> tickerCache = new ConcurrentHashMap<>();
    private volatile boolean isConnected = false;
    private volatile boolean shouldReconnect = true;

    @PostConstruct
    public void init() {
        // 启动时连接WebSocket
        connectWebSocket();
        
        // 启动定时任务，将缓存的数据写入Redis
        startRedisUpdateTask();
    }

    @PreDestroy
    public void destroy() {
        shouldReconnect = false;
        if (webSocketClient != null) {
            webSocketClient.close();
        }
        scheduler.shutdown();
    }

    private void connectWebSocket() {
        try {
            List<String> symbols = exchangePairInfoMapper.getEnabledPairNames();
            if (symbols == null || symbols.isEmpty()) {
                log.info("没有启用的交易对，无需连接币安WebSocket");
                return;
            }

            // 构建WebSocket URL - 使用combined stream
            String streams = symbols.stream()
                    .map(symbol -> symbol.toLowerCase() + "@ticker")
                    .collect(Collectors.joining("/"));

            String wsUrl = "wss://stream.binance.com:9443/stream?streams=" + streams;
            log.info("连接币安WebSocket: {}", wsUrl);

            webSocketClient = new WebSocketClient(URI.create(wsUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("币安WebSocket连接已建立");
                    isConnected = true;
                }

                @Override
                public void onMessage(String message) {
                    processMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn("币安WebSocket连接关闭: {} - {}", code, reason);
                    isConnected = false;
                    scheduleReconnect();
                }

                @Override
                public void onError(Exception ex) {
                    log.error("币安WebSocket连接错误", ex);
                    isConnected = false;
                    scheduleReconnect();
                }
            };

            webSocketClient.connect();

        } catch (Exception e) {
            log.error("连接币安WebSocket失败", e);
            scheduleReconnect();
        }
    }

    private void scheduleReconnect() {
        if (!shouldReconnect) return;

        scheduler.schedule(() -> {
            if (shouldReconnect) {
                log.info("尝试重新连接币安WebSocket...");
                connectWebSocket();
            }
        }, 5, TimeUnit.SECONDS);
    }

    private void startRedisUpdateTask() {
        // 每3秒将缓存的ticker数据写入Redis
        scheduler.schedule(this::updateRedisData, 3, TimeUnit.SECONDS);
    }

    private void updateRedisData() {
        if (!shouldReconnect) return;

        try {
            if (!tickerCache.isEmpty()) {
                for (BinanceTickerResponse ticker : tickerCache.values()) {

                    String key = "binance:ticker:" + ticker.symbol;
                    String value = objectMapper.writeValueAsString(ticker);
//                    log.info("更新Redis数据: {}", value);
                    stringRedisTemplate.opsForValue().set(key, value, 5, TimeUnit.SECONDS);
                }
                log.debug("更新Redis数据成功，交易对数: {}", tickerCache.size());
            }
        } catch (Exception e) {
            log.error("更新Redis数据失败", e);
        }

        // 继续下一次更新
        scheduler.schedule(this::updateRedisData, 3, TimeUnit.SECONDS);
    }



    private void processMessage(String message) {
        try {
            JsonNode root = objectMapper.readTree(message);
            
            // 检查是否是combined stream格式
            if (root.has("stream") && root.has("data")) {
                JsonNode data = root.get("data");
                String eventType = data.get("e").asText();
                
                if ("24hrTicker".equals(eventType)) {
                    // 手动转换WebSocket数据为REST API格式
                    BinanceTickerResponse ticker = new BinanceTickerResponse();

                    // 直接从WebSocket数据中提取并转换为REST API字段
                    ticker.symbol = data.get("s").asText();
                    ticker.priceChange = data.get("p").asText();
                    ticker.priceChangePercent = data.get("P").asText();
                    ticker.weightedAvgPrice = data.get("w").asText();
                    ticker.prevClosePrice = data.get("o").asText(); // 使用openPrice作为prevClosePrice
                    ticker.lastPrice = data.get("c").asText();
                    ticker.lastQty = data.get("Q").asText();
                    ticker.bidPrice = data.get("c").asText(); // 使用lastPrice作为bidPrice
                    ticker.bidQty = "0.00000000"; // WebSocket没有此数据
                    ticker.askPrice = data.get("c").asText(); // 使用lastPrice作为askPrice
                    ticker.askQty = "0.00000000"; // WebSocket没有此数据
                    ticker.openPrice = data.get("o").asText();
                    ticker.highPrice = data.get("h").asText();
                    ticker.lowPrice = data.get("l").asText();
                    ticker.volume = data.get("v").asText();
                    ticker.quoteVolume = data.get("q").asText();
                    ticker.openTime = data.get("O").asLong();
                    ticker.closeTime = data.get("C").asLong();
                    ticker.firstId = data.get("F").asLong();
                    ticker.lastId = data.get("L").asLong();
                    ticker.count = data.get("n").asLong();

                    if (ticker.symbol != null) {
                        tickerCache.put(ticker.symbol, ticker);
                        log.debug("更新ticker缓存: {}", ticker.symbol);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析WebSocket消息失败: {}", message, e);
        }
    }

    // 币安24hr行情返回结构（完全按照REST API格式）
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BinanceTickerResponse {
        // 完全使用REST API的字段名，不使用WebSocket的字段映射
        public String symbol;
        public String priceChange;
        public String priceChangePercent;
        public String weightedAvgPrice;
        public String prevClosePrice;
        public String lastPrice;
        public String lastQty;
        public String bidPrice;
        public String bidQty;
        public String askPrice;
        public String askQty;
        public String openPrice;
        public String highPrice;
        public String lowPrice;
        public String volume;
        public String quoteVolume;
        public long openTime;
        public long closeTime;
        public long firstId;
        public long lastId;
        public long count;
    }
}
