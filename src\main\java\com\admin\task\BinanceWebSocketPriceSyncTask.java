package com.admin.task;

import com.admin.mapper.ExchangePairMapper;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.URI;
import java.util.List;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BinanceWebSocketPriceSyncTask {

    @Autowired
    private ExchangePairMapper exchangePairInfoMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    private WebSocketClient webSocketClient;
    private final ConcurrentHashMap<String, BinanceTickerResponse> tickerCache = new ConcurrentHashMap<>();
    private volatile boolean isConnected = false;
    private volatile boolean shouldReconnect = true;

    @PostConstruct
    public void init() {
        // 启动时连接WebSocket
        connectWebSocket();
        
        // 启动定时任务，将缓存的数据写入Redis
        startRedisUpdateTask();
    }

    @PreDestroy
    public void destroy() {
        shouldReconnect = false;
        if (webSocketClient != null) {
            webSocketClient.close();
        }
        scheduler.shutdown();
    }

    private void connectWebSocket() {
        try {
            List<String> symbols = exchangePairInfoMapper.getEnabledPairNames();
            if (symbols == null || symbols.isEmpty()) {
                log.info("没有启用的交易对，无需连接币安WebSocket");
                return;
            }

            // 构建WebSocket URL - 使用combined stream
            String streams = symbols.stream()
                    .map(symbol -> symbol.toLowerCase() + "@ticker")
                    .collect(Collectors.joining("/"));

            String wsUrl = "wss://stream.binance.com:9443/stream?streams=" + streams;
            log.info("连接币安WebSocket: {}", wsUrl);

            webSocketClient = new WebSocketClient(URI.create(wsUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("币安WebSocket连接已建立");
                    isConnected = true;
                }

                @Override
                public void onMessage(String message) {
                    processMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn("币安WebSocket连接关闭: {} - {}", code, reason);
                    isConnected = false;
                    scheduleReconnect();
                }

                @Override
                public void onError(Exception ex) {
                    log.error("币安WebSocket连接错误", ex);
                    isConnected = false;
                    scheduleReconnect();
                }
            };

            webSocketClient.connect();

        } catch (Exception e) {
            log.error("连接币安WebSocket失败", e);
            scheduleReconnect();
        }
    }

    private void scheduleReconnect() {
        if (!shouldReconnect) return;

        scheduler.schedule(() -> {
            if (shouldReconnect) {
                log.info("尝试重新连接币安WebSocket...");
                connectWebSocket();
            }
        }, 5, TimeUnit.SECONDS);
    }

    private void startRedisUpdateTask() {
        // 每3秒将缓存的ticker数据写入Redis
        scheduler.schedule(this::updateRedisData, 3, TimeUnit.SECONDS);
    }

    private void updateRedisData() {
        if (!shouldReconnect) return;

        try {
            if (!tickerCache.isEmpty()) {
                for (BinanceTickerResponse ticker : tickerCache.values()) {

                    String key = "binance:ticker:" + ticker.symbol;
                    String value = objectMapper.writeValueAsString(ticker);
                    log.info("更新Redis数据: {}", value);
                    stringRedisTemplate.opsForValue().set(key, value, 5, TimeUnit.SECONDS);
                }
                log.debug("更新Redis数据成功，交易对数: {}", tickerCache.size());
            }
        } catch (Exception e) {
            log.error("更新Redis数据失败", e);
        }

        // 继续下一次更新
        scheduler.schedule(this::updateRedisData, 3, TimeUnit.SECONDS);
    }



    private void processMessage(String message) {
        try {
            JsonNode root = objectMapper.readTree(message);
            
            // 检查是否是combined stream格式
            if (root.has("stream") && root.has("data")) {
                JsonNode data = root.get("data");
                String eventType = data.get("e").asText();
                
                if ("24hrTicker".equals(eventType)) {
                    BinanceTickerResponse ticker = objectMapper.treeToValue(data, BinanceTickerResponse.class);
                    if (ticker != null && ticker.symbol != null) {
                        // 设置WebSocket中没有的字段，使其与REST API格式一致
                        ticker.prevClosePrice = ticker.openPrice; // 使用openPrice作为prevClosePrice
                        ticker.bidPrice = ticker.lastPrice; // 使用lastPrice作为bidPrice的近似值
                        ticker.bidQty = "0.00000000"; // WebSocket没有此数据
                        ticker.askPrice = ticker.lastPrice; // 使用lastPrice作为askPrice的近似值
                        ticker.askQty = "0.00000000"; // WebSocket没有此数据

                        tickerCache.put(ticker.symbol, ticker);
                        log.debug("更新ticker缓存: {}", ticker.symbol);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析WebSocket消息失败: {}", message, e);
        }
    }

    // 币安24hr行情返回结构（与REST API保持一致）
    // WebSocket字段映射：s->symbol, p->priceChange, P->priceChangePercent, w->weightedAvgPrice,
    // c->lastPrice, Q->lastQty, o->openPrice, h->highPrice, l->lowPrice, v->volume, q->quoteVolume,
    // O->openTime, C->closeTime, F->firstId, L->lastId, n->count
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BinanceTickerResponse {
        // WebSocket字段名映射到REST API字段名
        @com.fasterxml.jackson.annotation.JsonProperty("s")
        public String symbol;
        @com.fasterxml.jackson.annotation.JsonProperty("p")
        public String priceChange;
        @com.fasterxml.jackson.annotation.JsonProperty("P")
        public String priceChangePercent;
        @com.fasterxml.jackson.annotation.JsonProperty("w")
        public String weightedAvgPrice;

        // WebSocket没有这个字段，使用openPrice作为替代
        public String prevClosePrice;

        @com.fasterxml.jackson.annotation.JsonProperty("c")
        public String lastPrice;
        @com.fasterxml.jackson.annotation.JsonProperty("Q")
        public String lastQty;

        // WebSocket没有bid/ask数据，设为null
        public String bidPrice;
        public String bidQty;
        public String askPrice;
        public String askQty;

        @com.fasterxml.jackson.annotation.JsonProperty("o")
        public String openPrice;
        @com.fasterxml.jackson.annotation.JsonProperty("h")
        public String highPrice;
        @com.fasterxml.jackson.annotation.JsonProperty("l")
        public String lowPrice;
        @com.fasterxml.jackson.annotation.JsonProperty("v")
        public String volume;
        @com.fasterxml.jackson.annotation.JsonProperty("q")
        public String quoteVolume;
        @com.fasterxml.jackson.annotation.JsonProperty("O")
        public long openTime;
        @com.fasterxml.jackson.annotation.JsonProperty("C")
        public long closeTime;
        @com.fasterxml.jackson.annotation.JsonProperty("F")
        public String firstId;
        @com.fasterxml.jackson.annotation.JsonProperty("L")
        public String lastId;
        @com.fasterxml.jackson.annotation.JsonProperty("n")
        public String count;
        // ... 可根据实际需要补充字段
    }
}
