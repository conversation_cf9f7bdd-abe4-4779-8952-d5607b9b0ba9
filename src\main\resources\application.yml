server:
  port: 8081
  servlet:
    context-path: /

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************
    username: root
    password: 123456
  sql:
    init:
      mode: never
      schema-locations: classpath:db/schema.sql
      encoding: UTF-8

  mail:
    host: smtp.qq.com
    port: 465
    username:
    password:
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
          starttls:
            enable: true
            required: true
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  mvc:
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600
  task:
    scheduling:
      pool:
        size: 5
      thread-name-prefix: schedule-

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.admin.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl 

logging:
  level:
    root: warn
    com.admin: info
    org.springframework.web: warn
    org.springframework.security: warn 

# 文件上传配置
upload:
  env: prod # local=本地，prod=线上
  path-local: E:/data/server/upload
  path-prod: /data/server/upload
  access-path: /upload/**
  # path: 由代码动态设置
#  map-path: file:${upload.path}

#分发配置
alipay:
  api:
    GATEWAY:  https://openapi.hzqiuyukj.com
    APP_PRIVATE_KEY:
    OPENAPI_PUBLIC_KEY:
    APP_ID:

binance:
  spot-ticker-url: https://api.binance.com/api/v1/ticker/24hr?symbol=
  futures-ticker-url: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=

web3:
  bsc:
    rpc-urls:
      - https://bsc-dataseed1.bnbchain.org
      - https://bsc-dataseed2.bnbchain.org
      - https://bsc-dataseed3.bnbchain.org
      - https://bsc-dataseed4.bnbchain.org
      - https://bsc.publicnode.com
    usdt-contract: 0x55d398326f99059fF775485246999027B3197955