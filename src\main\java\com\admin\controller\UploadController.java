package com.admin.controller;

import com.admin.annotation.Log;
import com.admin.common.utils.R;
import com.admin.config.UploadPathConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/upload")
public class UploadController {

    @Autowired
    private UploadPathConfig uploadPathConfig;

    @Log(title = "上传Banner图片", operType = "上传")
    @PostMapping("/banner")
    public R uploadBanner(@RequestParam("file") MultipartFile file) {
        return uploadImage(file, "banner");
    }

    @Log(title = "上传Logo图片", operType = "上传")
    @PostMapping("/logo")
    public R uploadLogo(@RequestParam("file") MultipartFile file) {
        return uploadImage(file, "logo");
    }

    @Log(title = "上传头像图片", operType = "上传")
    @PostMapping("/avatar")
    public R uploadAvatar(@RequestParam("file") MultipartFile file) {
        return uploadImage(file, "avatar");
    }

    /**
     * 通用图片上传方法
     */
    private R uploadImage(MultipartFile file, String type) {
        try {
            // 检查文件类型
            String contentType = file.getContentType();
            if (!"image/jpeg".equals(contentType) && !"image/png".equals(contentType)) {
                return R.error("只支持 JPG 和 PNG 格式的图片");
            }

            // 检查文件大小
            if (file.getSize() > 2 * 1024 * 1024) {
                return R.error("图片大小不能超过 2MB");
            }

            // 生成文件保存路径
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String directory = uploadPathConfig.getUploadPath() + File.separator + type + File.separator + datePath;
            File dir = new File(directory);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成新的文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String newFileName = UUID.randomUUID().toString().replaceAll("-", "") + extension;
            
            // 保存文件
            File destFile = new File(directory + File.separator + newFileName);
            file.transferTo(destFile);

            // 返回可访问的URL路径
            String url = "/upload/" + type + "/" + datePath + "/" + newFileName;
            return R.ok().put("data", url);
            
        } catch (Exception e) {
            log.error("上传图片失败", e);
            return R.error("上传图片失败");
        }
    }
} 